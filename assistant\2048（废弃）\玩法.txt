========================正式资料====================================
游戏名
-2048无尽成就

目标：
-考虑先上架taptap
-如果收入达到5000，可以考虑加强一波然后上架，鸿蒙，抖音，小程序等，否则作罢

具体逻辑参考原型

统计项：
-游戏时长
-游戏次数
-最高得分
-最低得分
-解锁成就
-解锁乐器
-解锁乐谱
-解锁主题
-解锁背景
-完整弹奏
-捐助次数
-获得金币
-花费金币
-开启宝箱


金币
-1分等于1金币
-金币可以用来撤销
1-免费，2-1000，3-5000，4-25000，5-125000

宝箱
-开启一个宝箱需要50000金币
-50%概率，1-25000金币
-10%概率，25001-50000金币
-5%概率，50001-10000金币
-4%概率，100001-200000金币
-1%概率，1000000金币
-10%概率，乐谱，如果齐了就返还50000
-10%概率，背景，如果齐了就返还50000
-5%概率，主题，如果齐了就返还75000
-5%概率，乐器，如果齐了就返还75000



成就系统：
名称，是否解锁，解锁次数（无尽成就才有），描述，等级（1-7），解锁时间，奖励。
当成就 被解锁时，记录解锁时间，如果一个成就是 无尽成就，第二次满足解锁条件时，解锁次数+1
成就的奖励可以是专属背景，主题，乐谱
7个等级的成就以及无尽成就的徽章背景不同
成就分稀有度，灰色，白色，绿色，蓝色，紫色，金色，红色，奖励金币5000，10000，20000，50000，100000，200000，500000


传统成就
2，获得砖块2，灰色
4，获得砖块4，灰色
8，获得砖块8，灰色
16，获得砖块16，灰色
32，获得砖块32，灰色
64，获得砖块64，白色
128，获得砖块128，白色
256，获得砖块256，绿色
512，获得砖块512，蓝色
1024，获得砖块1024，紫色
2048，获得砖块2048，金色
4096，获得砖块4096，金色
8192，获得砖块8192，金色
16384，获得砖块16384，金色
32768，获得砖块32768，金色
65536，获得砖块65536，红色，额外奖励背景
‌131072，获得砖块‌131072，红色，额外奖励背景

得分
1024，码农的祝福，金色
2048，点题，金色
520，我爱你，金色，额外奖励背景
401912，我是AI，红色，额外奖励背景
10000+，突破，蓝色
100000+，极限，金色
1000000+，超越极限，红色，额外奖励背景
3860000+，真-超越极限，额外奖励主题，额外奖励背景

888，发发发，紫色
8888，财源滚滚，金色
88888，‌金玉满堂，红色，额外奖励背景
888888，富甲天下，红色，额外奖励主题，额外奖励背景

100，获得满分！？，蓝色
1000，出类拔萃，紫色
10000，超凡脱俗，金色
100000，卓尔不群，红色，额外奖励背景
1000000，超凡入圣，红色，额外奖励主题，额外奖励背景


事件
初窥门径，完成1次游戏，灰色
登堂入室，完成10次游戏，白色
渐入佳境，完成20次游戏，绿色
驾轻就熟，完成40次游戏，蓝色
目无全牛，完成60次游戏，紫色
融会贯通‌，完成80次游戏，金色
千锤百炼，完成100次游戏，红色，额外奖励背景

持之以恒，游戏时长超过1小时，白色
积水成渊，游戏时长超过10小时，蓝色
日积月累，游戏时长超过25小时，金色
水滴石穿，游戏时长超过50小时，红色，额外奖励背景

‌余音袅袅，弹奏完整曲目10首，白色
娓娓动听，弹奏完整曲目100首，蓝色，额外奖励乐谱
‌震天动地，弹奏完整曲目500首，金色，额外奖励乐谱
大音乐家，弹奏完整曲目1000首，红色，额外奖励乐谱，额外奖励乐器

完美落幕，当乐谱播放结束时，正好游戏结束，紫色
戛然而止，当乐新乐谱刚播放第一个音节时，正好游戏结束，紫色
乐谱收藏家，集齐所有乐谱，红色，额外奖励背景
乐器大师，集齐所有乐器，红色，额外奖励背景
这是我的节奏，在10秒内，完成一首乐谱的播放，金色


乐于助人，第一次完成捐助，蓝色
乐善好施，累计5次完成捐助，金色
仁怀天下，累计10次完成捐助，红色，额外奖励背景

富翁，获得1000000+金币，蓝色
大富翁，获得10000000+金币，金色
世界首富，获得100000000+金币，红色，额外奖励背景

菜鸟入场，打开宝箱1次，白色
渐入佳境，打开宝箱10次，蓝色
金色传说，打开宝箱100次，金色
传奇圣手，打开宝箱1000次，红色，额外奖励背景

反复横跳，累计撤销100次，绿色
穿越时空，累计撤销100000次，红色，额外奖励背景




--无尽成就（可以反复获取，记录数量）（都是红色成就）（成就点*n）
心心相印（n），每获得一次520520，n+1，额外奖励背景（该背景会展示获得次数，**重）
福星高照（n），每获得一次88888，n+1，额外奖励背景（该背景会展示获得次数，**重）
巅峰突破（n），分数大于100000时，每超越一次n+1，额外奖励背景（该背景会展示获得次数，**重）
‌炉火纯青（n），每完成100盘游戏，n+1，额外奖励背景（该背景会展示获得次数，**重）
厚积薄发（n），游戏时长每50h，n+1，额外奖励背景（该背景会展示获得次数，**重）
钧天之乐（n），每弹奏完整曲目1000首，n+1，额外奖励背景（该背景会展示获得次数，**重）
上善若水（n），每捐助10次，n+1，额外奖励背景（该背景会展示获得次数，**重）















待处理：
-可以导出存档（加密），优先云存档





========================灵感随笔====================================













=============================废弃或者其他======================================



-音乐音效系统
--本身无音乐，行动时或者点击菜单时有音效
--音效组合形成乐曲，每走一步都是弹奏一步（比如钢琴）
--音效模式（全局固定，本局固定，局间随机，完全随机）
--音效池
--随机模式，打开游戏时，以及一局结束时开始随机

--乐器有多种可选


提示词参考：
我现在想用midjourney生成游戏的ui界面，麻烦帮我生成midjourney的提示词。以下是需求描述。
这是一个2048游戏，现在想要生成游戏的成就面板的弹窗ui。
弹窗头部有标题 和 一个 关闭按钮。
内容部分 有上下两个区块。
上区块，会展示一些 统计项，比如 已解锁成就数/成就总数，获得的成就点数。
下区块，是 成就列表，成就列表中是成就项，成就列表有滚动条。
每个 列表项 都会分成了 左右两部分，左边部分是 正方形区块 里面会展示 成就 名称（文字即可，没有图标），
右侧 部分 分为两行， 第一行 是 成就的 描述，第二行 是 成就获得时间，如果该成就 没有 解锁，那么会展示 未解锁的样式。
‌色彩系统‌：使用2048标志性的柔和橙/米色系，搭配深灰文字保证可读性。


帮我生成midjourny的提示词，描述如下，科幻风格游戏背景，简洁风格
帮我生成midjourny的提示词，描述如下，这是一个游戏的水墨风格背景图，描述的是仙侠世界，有灵兽，神仙
帮我生成midjourny的提示词，描述如下，这是一个游戏的背景图，描述的是震撼的宇宙