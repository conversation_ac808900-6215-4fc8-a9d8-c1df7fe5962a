{"templatePinStates": [], "dependencyTypeInfos": [{"userAdded": false, "type": "UnityEngine.AnimationClip", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEditor.Animations.AnimatorController", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.AnimatorOverrideController", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEditor.Audio.AudioMixerController", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.ComputeShader", "ignore": true, "defaultInstantiationMode": 1, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Cubemap", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.GameObject", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEditor.LightingDataAsset", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": false}, {"userAdded": false, "type": "UnityEngine.LightingSettings", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Material", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEditor.MonoScript", "ignore": true, "defaultInstantiationMode": 1, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.PhysicMaterial", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.PhysicsMaterial2D", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Rendering.PostProcessing.PostProcessProfile", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Rendering.PostProcessing.PostProcessResources", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Rendering.VolumeProfile", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEditor.SceneAsset", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": false}, {"userAdded": false, "type": "UnityEngine.Shader", "ignore": true, "defaultInstantiationMode": 1, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.ShaderVariantCollection", "ignore": true, "defaultInstantiationMode": 1, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Texture", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Texture2D", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}, {"userAdded": false, "type": "UnityEngine.Timeline.TimelineAsset", "ignore": false, "defaultInstantiationMode": 0, "supportsModification": true}], "defaultDependencyTypeInfo": {"userAdded": false, "type": "<default_scene_template_dependencies>", "ignore": false, "defaultInstantiationMode": 1, "supportsModification": true}, "newSceneOverride": 0}