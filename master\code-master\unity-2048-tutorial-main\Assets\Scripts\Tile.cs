using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class Tile : MonoBehaviour
{
    public TileState state { get; private set; }
    public TileCell cell { get; private set; }
    public bool locked { get; set; }

    private Image background;
    private TextMeshProUGUI text;

    private void Awake()
    {
        background = GetComponent<Image>();
        text = GetComponentInChildren<TextMeshProUGUI>();
    }

    public void SetState(TileState state)
    {
        this.state = state;

        // 获取并应用当前主题的颜色配置
        Theme currentTheme = ThemeManager.Instance.GetCurrentTheme();
        if (currentTheme != null)
        {
            TileColorConfig config = currentTheme.GetTileConfig(state.number);
            if (config != null)
            {
                background.color = config.backgroundColor;
                text.color = config.textColor;
                state.backgroundColor = config.backgroundColor;
                state.textColor = config.textColor;
            }
        }
        text.text = state.number.ToString();
    }

    public void Spawn(TileCell cell)
    {
        if (this.cell != null) {
            this.cell.tile = null;
        }

        this.cell = cell;
        this.cell.tile = this;

        transform.position = cell.transform.position;
        StartCoroutine(AnimateSpawn());
    }

    private IEnumerator AnimateSpawn()
    {
        transform.localScale = Vector3.zero;
        background.color = new Color(background.color.r, background.color.g, background.color.b, 0f);
        text.color = new Color(text.color.r, text.color.g, text.color.b, 0f);

        float elapsed = 0f;
        float duration = 0.15f;

        while (elapsed < duration)
        {
            float progress = elapsed / duration;
            transform.localScale = Vector3.Lerp(Vector3.zero, Vector3.one, progress);
            
            Color bgColor = background.color;
            Color txtColor = text.color;
            background.color = new Color(bgColor.r, bgColor.g, bgColor.b, progress);
            text.color = new Color(txtColor.r, txtColor.g, txtColor.b, progress);
            
            elapsed += Time.deltaTime;
            yield return null;
        }

        transform.localScale = Vector3.one;
        background.color = new Color(background.color.r, background.color.g, background.color.b, 1f);
        text.color = new Color(text.color.r, text.color.g, text.color.b, 1f);
    }

    public void MoveTo(TileCell cell)
    {
        if (this.cell != null) {
            this.cell.tile = null;
        }

        this.cell = cell;
        this.cell.tile = this;

        StartCoroutine(Animate(cell.transform.position, false));
    }

    public void Merge(TileCell cell)
    {
        if (this.cell != null) {
            this.cell.tile = null;
        }

        this.cell = null;
        cell.tile.locked = true;

        StartCoroutine(Animate(cell.transform.position, true));
    }

    private IEnumerator Animate(Vector3 to, bool merging)
    {
        float elapsed = 0f;
        float duration = 0.1f;

        Vector3 from = transform.position;

        while (elapsed < duration)
        {
            transform.position = Vector3.Lerp(from, to, elapsed / duration);
            elapsed += Time.deltaTime;
            yield return null;
        }

        transform.position = to;

        if (merging) {
            Destroy(gameObject);
        }
    }

}
