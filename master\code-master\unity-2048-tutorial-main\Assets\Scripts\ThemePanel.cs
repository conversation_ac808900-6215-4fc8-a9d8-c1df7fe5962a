using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ThemePanel : MonoBehaviour
{
    [SerializeField] private Transform themeButtonContainer;
    [SerializeField] private Button themeButtonPrefab;
    [SerializeField] private Button closeButton;

    private void Start()
    {
        if (closeButton != null)
        {
            closeButton.onClick.AddListener(() => gameObject.SetActive(false));
        }

        InitializeThemeButtons();
    }

    private void InitializeThemeButtons()
    {
        Theme[] availableThemes = ThemeManager.Instance.GetAvailableThemes();
        if (availableThemes == null) return;

        foreach (var theme in availableThemes)
        {
            Button button = Instantiate(themeButtonPrefab, themeButtonContainer);
            TextMeshProUGUI buttonText = button.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = theme.themeName;
            }

            // 设置按钮背景色为主题的棋盘背景色
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                buttonImage.color = theme.boardBackgroundColor;
            }

            button.onClick.AddListener(() => OnThemeSelected(theme));
        }
    }

    private void OnThemeSelected(Theme theme)
    {
        ThemeManager.Instance.SetTheme(theme);
    }
}