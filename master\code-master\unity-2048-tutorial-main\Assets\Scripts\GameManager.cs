using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

[DefaultExecutionOrder(-1)]
public class GameManager : MonoBehaviour
{
    private MusicManager musicManager;
    public static GameManager Instance { get; private set; }

    [SerializeField] private TileBoard board;
    [SerializeField] private CanvasGroup gameOver;
    [SerializeField] private TextMeshProUGUI scoreText;
    [SerializeField] private TextMeshProUGUI hiscoreText;
    [SerializeField] private TextMeshProUGUI statsText; // 用于显示游戏统计信息
    [SerializeField] private AchievementPopup achievementPopupPrefab; // 成就提示UI预制体
    [SerializeField] private Transform achievementCanvas; // 用于放置成就UI的Canvas
    [SerializeField] private AchievementPanel achievementPanel; // 成就列表面板
    [SerializeField] private Transform settingPanel; // 成就列表面板
    [SerializeField] private ThemePanel themePanel; // 主题选择面板
    private AchievementPopup achievementPopup; // 成就提示UI实例

    public int score { get; private set; } = 0;

    private void Awake()
    {
        if (Instance != null) {
            DestroyImmediate(gameObject);
        } else {
            Instance = this;
        }

    }

    private void OnDestroy()
    {
        if (Instance == this) {
            Instance = null;
        }
    }

    private void Start()
    {
        // 初始化音乐管理器
        musicManager = MusicManager.Instance;
        // 加载默认乐器
        musicManager.LoadInstrument("Piano");
        
        // 初始化成就UI
        if (achievementPopupPrefab != null && achievementCanvas != null)
        {
            achievementPopup = Instantiate(achievementPopupPrefab);
            achievementPopup.Initialize(achievementCanvas);
        }
        
        LoadGameData();
        NewGame();
    }



    private GameData gameData = new GameData();

    public void ShowAchievementPanel()
    {
        if (achievementPanel != null)
        {
            achievementPanel.gameObject.SetActive(true);
            achievementPanel.ShowAchievements(gameData.achievements);
        }
    }

    public void ShowSettingPanel()
    {
        if (settingPanel != null)
        {
            settingPanel.gameObject.SetActive(true);
        }
    }

    public void HideSettingPanel()
    {
        if (settingPanel != null)
        {
            settingPanel.gameObject.SetActive(false);
        }
    }

    public void ShowThemePanel()
    {
        if (themePanel != null)
        {
            themePanel.gameObject.SetActive(true);
        }
    }

    public void NewGame()
    {
        // reset score
        SetScore(0);
        hiscoreText.text = gameData.hiscore.ToString();
        UpdateStatsDisplay();

        // hide game over screen
        gameOver.alpha = 0f;
        gameOver.interactable = false;

        // update board state
        board.ClearBoard();
        board.CreateTile();
        board.CreateTile();
        board.enabled = true;

        // 记录游戏开始时间
        gameData.currentGameStartTime = Time.time;
        gameData.totalGames++;

        // 播放音乐
        musicManager.PlayNextNote();


        //achievementPopup.ShowAchievement("测试", "测试123");
        //achievementPopup.ShowAchievement("测试2", "测试123");
        //achievementPopup.ShowAchievement("测试3", "测试123");
        //achievementPopup.ShowAchievement("测试4", "测试123");

    }

    public void GameOver()
    {
        StartCoroutine(GameOverRoutine());
    }

    private IEnumerator GameOverRoutine()
    {
        board.enabled = false;
        gameOver.interactable = true;

        // 更新游戏统计数据
        float gameTime = Time.time - gameData.currentGameStartTime;
        gameData.totalPlayTime += gameTime;

        // 更新最低分记录
        if (gameData.lowestScore == 0 || score < gameData.lowestScore)
        {
            gameData.lowestScore = score;
        }

        // 检查并更新"第一次"成就
        CheckFirstTimeAchievement();

        // 保存游戏数据
        SaveGameData();

      

        // 显示游戏结束界面
        yield return StartCoroutine(Fade(gameOver, 1f, 0.2f));

          // 播放结束音乐并等待播放完成
        yield return StartCoroutine(musicManager.PlayNNotesCoroutine(10));
    }

    private IEnumerator Fade(CanvasGroup canvasGroup, float to, float delay = 0f)
    {
        yield return new WaitForSeconds(delay);

        float elapsed = 0f;
        float duration = 0.5f;
        float from = canvasGroup.alpha;

        while (elapsed < duration)
        {
            canvasGroup.alpha = Mathf.Lerp(from, to, elapsed / duration);
            elapsed += Time.deltaTime;
            yield return null;
        }

        canvasGroup.alpha = to;
    }

    public void IncreaseScore(int points)
    {
        SetScore(score + points);
    }

    private void SetScore(int score)
    {
        this.score = score;
        scoreText.text = score.ToString();

        //SaveHiscore();
    }

    //private void SaveHiscore()
    //{
    //    int hiscore = LoadHiscore();

    //    if (score > hiscore) {
    //        PlayerPrefs.SetInt("hiscore", score);
    //    }
    //}

    //private int LoadHiscore()
    //{
    //    return PlayerPrefs.GetInt("hiscore", 0);
    //}



    [System.Serializable]
    public class Achievement
    {
        public string name;
        public bool isUnlocked;
        public string description;
        public string unlockDate;
        public int unlockCount = 0;  // 解锁次数
        public bool isEndless;       // 是否是无尽成就
        public int level;            // 成就等级（1-7）
    }

    [System.Serializable]
    private class GameData
    {
        public int hiscore;
        public int totalGames;
        public float totalPlayTime;
        public int lowestScore;
        public float currentGameStartTime;
        public List<Achievement> achievements = new List<Achievement>();
    }

    private string SavePath => Path.Combine(Application.persistentDataPath, "game_data.dat");
    private const string EncryptionKey = "YourEncryptionKey123"; // �޸�Ϊ�����Կ

    // ... [Awake��OnDestroy��Start�ȷ������ֲ���]...

    private void SaveGameData()
    {
        if (score > gameData.hiscore)
        {
            gameData.hiscore = score;
        }
        string json = JsonUtility.ToJson(gameData);
        string encryptedData = Encrypt(json);
        File.WriteAllText(SavePath, encryptedData);
    }

    private void LoadGameData()
    {
        if (File.Exists(SavePath))
        {
            try
            {
                string encryptedData = File.ReadAllText(SavePath);
                string json = Decrypt(encryptedData);
                gameData = JsonUtility.FromJson<GameData>(json);
            }
            catch
            {
                Debug.LogError("Failed to load game data");
                InitializeNewGameData();
            }
        }
        else
        {
            InitializeNewGameData();
        }
    }

    private void InitializeNewGameData()
    {
        gameData = new GameData();
        // 初始化"第一次"成就
        Achievement firstTimeAchievement = new Achievement
        {
            name = "初窥门径",
            description = "完成1次游戏",
            isUnlocked = false,
            isEndless = false,
            level = 1
        };
        gameData.achievements.Add(firstTimeAchievement);

        // 初始化"登堂入室"成就
        Achievement masterAchievement = new Achievement
        {
            name = "登堂入室",
            description = "完成10次游戏",
            isUnlocked = false,
            isEndless = false,
            level = 2
        };

        gameData.achievements.Add(masterAchievement);
     


    }

    // 重置存档数据（调试用）
    public void ResetGameData()
    {
        // 删除存档文件
        if (File.Exists(SavePath))
        {
            File.Delete(SavePath);
        }

        // 重新初始化游戏数据
        InitializeNewGameData();
        
        // 重置当前分数
        SetScore(0);
        
        // 更新界面显示
        UpdateStatsDisplay();
        
        Debug.Log("游戏存档已重置");
    }

    private void UpdateStatsDisplay()
    {
        if (statsText != null)
        {
            int hours = Mathf.FloorToInt(gameData.totalPlayTime / 3600);
            int minutes = Mathf.FloorToInt((gameData.totalPlayTime % 3600) / 60);
            int seconds = Mathf.FloorToInt(gameData.totalPlayTime % 60);

            string timeStr = string.Format("{0:D2}:{1:D2}:{2:D2}", hours, minutes, seconds);
            
            // 获取成就信息
            //string achievementsInfo = "\n成就:\n";
            //foreach (var achievement in gameData.achievements)
            //{
            //    string unlockInfo = achievement.isUnlocked
            //        ? $"已获得 (等级{achievement.level})"
            //        + (achievement.isEndless ? $", 解锁{achievement.unlockCount}次" : "")
            //        + (achievement.unlockDate != null ? $"\n解锁时间: {achievement.unlockDate}" : "")
            //        : "未获得";
                
            //    achievementsInfo += string.Format("{0}: {1}\n", 
            //        achievement.name,
            //        unlockInfo);
            //}

            statsText.text = string.Format(
                "游戏统计\n" +
                "总游戏次数: {0}\n" +
                "总游戏时长: {1}\n" +
                "最高得分: {2}\n" +
                "最低得分: {3}",
                gameData.totalGames,
                timeStr,
                gameData.hiscore,
                gameData.lowestScore == 0 ? "-" : gameData.lowestScore.ToString()
                //achievementsInfo
            );
        }
    }

    private string Encrypt(string plainText)
    {
        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
        byte[] keyBytes = Encoding.UTF8.GetBytes(EncryptionKey);

        for (int i = 0; i < plainBytes.Length; i++)
        {
            plainBytes[i] ^= keyBytes[i % keyBytes.Length];
        }
        return System.Convert.ToBase64String(plainBytes);
    }

    private void CheckFirstTimeAchievement()
    {
        var firstTimeAchievement = gameData.achievements.Find(a => a.name == "初窥门径");
        if (firstTimeAchievement != null)
        {
            if (!firstTimeAchievement.isUnlocked)
            {
                UnlockAchievement(firstTimeAchievement);
            }
            else if (firstTimeAchievement.isEndless)
            {
                // 如果是无尽成就，增加解锁次数
                firstTimeAchievement.unlockCount++;
                if (achievementPopup != null)
                {
                    achievementPopup.ShowAchievement(
                        firstTimeAchievement.name,
                        $"{firstTimeAchievement.description} (第{firstTimeAchievement.unlockCount}次)"
                    );
                }
            }
        }

        // 检查"登堂入室"成就
        var masterAchievement = gameData.achievements.Find(a => a.name == "登堂入室");
        if (masterAchievement != null && !masterAchievement.isUnlocked && gameData.totalGames >= 10)
        {
            UnlockAchievement(masterAchievement);
        }
    }

    private void UnlockAchievement(Achievement achievement)
    {
        achievement.isUnlocked = true;
        achievement.unlockCount = 1;
        achievement.unlockDate = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        
        if (achievementPopup != null)
        {
            achievementPopup.ShowAchievement(achievement.name, achievement.description);
        }
        Debug.Log($"解锁成就：{achievement.name}");
    }

    private string Decrypt(string encryptedText)
    {
        byte[] encryptedBytes = System.Convert.FromBase64String(encryptedText);
        byte[] keyBytes = Encoding.UTF8.GetBytes(EncryptionKey);

        for (int i = 0; i < encryptedBytes.Length; i++)
        {
            encryptedBytes[i] ^= keyBytes[i % keyBytes.Length];
        }
        return Encoding.UTF8.GetString(encryptedBytes);
    }




    public void LoadScene(string sceneName)
    {
        // 保存当前游戏状态
        SaveGameData();

        // 使用淡出效果
        StartCoroutine(LoadSceneWithTransition(sceneName));
    }

    private IEnumerator LoadSceneWithTransition(string sceneName)
    {
        // 淡出效果
        CanvasGroup fadeCanvas = gameOver; // 使用已有的CanvasGroup组件
        yield return StartCoroutine(Fade(fadeCanvas, 1f));

        // 加载新场景
        SceneManager.LoadScene(sceneName);
    }
}
