%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5136daff504972940813105a41d458e2, type: 3}
  m_Name: ClassicTheme
  m_EditorClassIdentifier: 
  themeName: Classic
  boardBackgroundColor: {r: 0.73333335, g: 0.6784314, b: 0.627451, a: 1}
  tileColors:
  - number: 2
    backgroundColor: {r: 0.933, g: 0.894, b: 0.855, a: 1}
    textColor: {r: 0.467, g: 0.431, b: 0.396, a: 1}
  - number: 4
    backgroundColor: {r: 0.929, g: 0.878, b: 0.784, a: 1}
    textColor: {r: 0.467, g: 0.431, b: 0.396, a: 1}
  - number: 8
    backgroundColor: {r: 0.949, g: 0.694, b: 0.475, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 16
    backgroundColor: {r: 0.961, g: 0.584, b: 0.388, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 32
    backgroundColor: {r: 0.965, g: 0.486, b: 0.373, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 64
    backgroundColor: {r: 0.965, g: 0.369, b: 0.231, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 128
    backgroundColor: {r: 0.929, g: 0.812, b: 0.447, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 256
    backgroundColor: {r: 0.929, g: 0.8, b: 0.38, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 512
    backgroundColor: {r: 0.929, g: 0.784, b: 0.314, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 1024
    backgroundColor: {r: 0.929, g: 0.773, b: 0.247, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
  - number: 2048
    backgroundColor: {r: 0.929, g: 0.761, b: 0.18, a: 1}
    textColor: {r: 0.973, g: 0.957, b: 0.945, a: 1}
