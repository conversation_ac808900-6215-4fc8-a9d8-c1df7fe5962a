using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using UnityEngine.EventSystems;

public class AchievementPanel : MonoBehaviour
{
    [SerializeField] private GameObject achievementItemPrefab;
    [SerializeField] private Transform contentContainer;
    [SerializeField] private Button closeButton;
    
    // 成就等级对应的颜色
    private static readonly Color[] LevelColors = new Color[]
    {
        new Color(0.5f, 0.5f, 0.5f),  // 灰色 - 等级1
        Color.white,                    // 白色 - 等级2
        new Color(0.0f, 1.0f, 0.0f),    // 绿色 - 等级3
        new Color(0.0f, 0.5f, 1.0f),    // 蓝色 - 等级4
        new Color(0.5f, 0.0f, 1.0f),    // 紫色 - 等级5
        new Color(1.0f, 0.84f, 0.0f),   // 金色 - 等级6
        new Color(1.0f, 0.0f, 0.0f)     // 红色 - 等级7
    };

    private void Start()
    {
        if (closeButton != null)
        {
            closeButton.onClick.AddListener(() => gameObject.SetActive(false));
        }
    }

    public void ShowAchievements(List<GameManager.Achievement> achievements)
    {
        // 清除现有的成就项，只删除包含AchievementItem组件的对象
        foreach (Transform child in contentContainer)
        {
            if (child.GetComponent<AchievementItem>() != null)
            {
                Destroy(child.gameObject);
            }
        }

        // 创建新的成就项
        foreach (var achievement in achievements)
        {
            GameObject itemGO = Instantiate(achievementItemPrefab, contentContainer);
            SetupAchievementItem(itemGO, achievement);
        }
    }

    private void SetupAchievementItem(GameObject itemGO, GameManager.Achievement achievement)
    {
        // 获取UI组件
        TextMeshProUGUI nameText = itemGO.transform.Find("NameText").GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI descriptionText = itemGO.transform.Find("DescriptionText").GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI unlockInfoText = itemGO.transform.Find("UnlockInfoText").GetComponent<TextMeshProUGUI>();

        // 设置名称（包括无尽成就的获得次数）
        string nameString = achievement.name;
        if (achievement.isUnlocked && achievement.isEndless)
        {
            nameString += $" ({achievement.unlockCount}次)";
        }
        nameText.text = nameString;

        // 设置描述
        descriptionText.text = achievement.description;

        // 设置解锁信息
        if (achievement.isUnlocked)
        {
            unlockInfoText.text = $"解锁时间: {achievement.unlockDate}";
            // 根据等级设置颜色
            Color levelColor = LevelColors[Mathf.Clamp(achievement.level - 1, 0, LevelColors.Length - 1)];
            // 1级成就使用浅灰色
            Color textColor = achievement.level == 1 ? new Color(0.6f, 0.6f, 0.6f) : Color.white;
            nameText.color = levelColor;
            descriptionText.color = textColor;
            unlockInfoText.color = textColor;
        }
        else
        {
            unlockInfoText.text = "未解锁";
            // 未解锁时使用深灰色
            Color darkGray = new Color(0.3f, 0.3f, 0.3f);
            nameText.color = darkGray;
            descriptionText.color = darkGray;
            unlockInfoText.color = darkGray;
        }
    }
}