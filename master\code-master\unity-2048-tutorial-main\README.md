# 2048

> 2048 is a puzzle game written by Italian web developer <PERSON><PERSON> and published on GitHub. The objective of the game is to slide numbered tiles on a grid to combine them until reaching the number 2048. It was originally written in JavaScript and CSS over a weekend, and released on March 9th, 2014 as free and open-source software.

- **Topics**: UI, Grids, Animation
- **Version**: Unity 2021.3 (LTS)
- [**Download**](https://github.com/zigurous/unity-2048-tutorial/archive/refs/heads/main.zip)
- [**Watch Video**](https://youtu.be/4NFZwPhqeRs)
