using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;

public class AchievementPopup : MonoBehaviour
{
    [SerializeField] private GameObject achievementPrefab;
    [SerializeField] private float showDuration = 3f;
    [SerializeField] private float fadeDuration = 0.5f;
    [SerializeField] private float spacing = 10f;

    private Transform parentTransform;
    private List<GameObject> activeAchievements = new List<GameObject>();
    private Queue<(string, string)> achievementQueue = new Queue<(string, string)>();
    private bool isProcessingQueue = false;

    public void Initialize(Transform parent)
    {
        parentTransform = parent;
    }

    public void ShowAchievement(string name, string description)
    {
        achievementQueue.Enqueue((name, description));
        if (!isProcessingQueue)
        {
            StartCoroutine(ProcessAchievementQueue());
        }
    }

    private IEnumerator ProcessAchievementQueue()
    {
        isProcessingQueue = true;

        while (achievementQueue.Count > 0)
        {
            var (name, description) = achievementQueue.Dequeue();

            GameObject achievementInstance = Instantiate(achievementPrefab, parentTransform);
            RectTransform rectTransform = achievementInstance.GetComponent<RectTransform>();
            
            // 设置新实例的位置
            if (activeAchievements.Count > 0)
            {
                // 获取最后一个成就的位置作为基准点
                RectTransform lastAchievementRect = activeAchievements[activeAchievements.Count - 1].GetComponent<RectTransform>();
                Vector2 basePosition = lastAchievementRect.anchoredPosition;
                
                // 在最后一个成就下方添加新的成就
                rectTransform.anchoredPosition = new Vector2(basePosition.x, basePosition.y - (rectTransform.rect.height + spacing));
            }

            // 设置成就内容
            TextMeshProUGUI[] texts = achievementInstance.GetComponentsInChildren<TextMeshProUGUI>();
            texts[0].text = name;        // 假设第一个Text组件是名称
            texts[1].text = description; // 假设第二个Text组件是描述

            CanvasGroup canvasGroup = achievementInstance.GetComponent<CanvasGroup>();
            canvasGroup.alpha = 0f;

            activeAchievements.Add(achievementInstance);
            StartCoroutine(ShowAndHideRoutine(achievementInstance, canvasGroup));

            // 等待1秒后显示下一个成就
            yield return new WaitForSeconds(1f);
        }

        isProcessingQueue = false;
    }

    private IEnumerator ShowAndHideRoutine(GameObject achievementInstance, CanvasGroup canvasGroup)
    {
        // 淡入效果
        yield return StartCoroutine(FadeRoutine(canvasGroup, 0f, 1f, fadeDuration));

        // 显示持续时间
        yield return new WaitForSeconds(showDuration);

        // 淡出效果
        yield return StartCoroutine(FadeRoutine(canvasGroup, 1f, 0f, fadeDuration));

        // 移除并销毁成就实例
        activeAchievements.Remove(achievementInstance);
        Destroy(achievementInstance);

        // 不再重新排列剩余的成就提示
    }

    private void RearrangeAchievements()
    {
        for (int i = 0; i < activeAchievements.Count; i++)
        {
            RectTransform rectTransform = activeAchievements[i].GetComponent<RectTransform>();
            float yOffset = i * (rectTransform.rect.height + spacing);
            rectTransform.anchoredPosition = new Vector2(0, -yOffset);
        }
    }

    private IEnumerator FadeRoutine(CanvasGroup canvasGroup, float from, float to, float duration)
    {
        float elapsed = 0f;

        while (elapsed < duration)
        {
            canvasGroup.alpha = Mathf.Lerp(from, to, elapsed / duration);
            elapsed += Time.deltaTime;
            yield return null;
        }

        canvasGroup.alpha = to;
    }
}