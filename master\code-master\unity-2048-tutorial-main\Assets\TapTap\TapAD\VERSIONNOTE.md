# Changelog

## 3.16.3.41
### New Features
- SLS 代码整理并增加采样逻辑
- 开屏广告增加曝光回调
### Improvements
- 广告下载流程代码整理
- 广告安装流程代码整理
### BugFix
- 获取 tapid 适配 tapsdk 4.x 版本
- 已安装应用列表问题修复

## 3.16.3.40
* oaid 优化

## 3.16.3.39

* oaid 优化,支持个人开发者
* 补充横幅、激励广告的点击回调
* crash 问题解决

## 3.16.3.37

- 广告内部优化

## 3.16.3.35

- layout behavior删除

## 3.16.3.34

- 开屏支持下载类广告预算
- 优化广告曝光链路逻辑
- 兼容 Android 14 & android sdk version 34
- 内部优化

## 3.16.3.32

- 内置浏览器下载优化,自适应屏幕方向优化
- 安装完成打开提醒，激活率提高
- 内部广告链路优化

## 3.16.3.30

- 新的广告预算适配;
- 原生广告支持模板类型广告;
- 开屏广告跳转落地页流程优化;
- 内部优化;
- Unity SDK初始化默认启用摇一摇;

## 3.16.3.29

### Internal Change

- 移除应用安装删除的监听器

## 3.16.3.2

* bugfix:
  1. 在申明权限的时候使用错了标签名 uses-permission 写成了 permission ，会导致用户在安装的时候只能安装一个申明了 "QUERY_ALL_PACKAGES" 权限的游戏

## 3.16.3.1
