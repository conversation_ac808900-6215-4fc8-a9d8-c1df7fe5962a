using UnityEngine;
using System.Collections.Generic;

public class ThemeManager : MonoBehaviour
{
    private static ThemeManager instance;
    public static ThemeManager Instance
    {
        get
        {
            if (instance == null)
            {
                GameObject go = new GameObject("ThemeManager");
                instance = go.AddComponent<ThemeManager>();
                DontDestroyOnLoad(go);
            }
            return instance;
        }
    }

    [SerializeField] private Theme[] availableThemes;
    
    private void LoadThemesFromResources()
    {
        // 加载Resources/Themes目录下的所有Theme资源
        Theme[] loadedThemes = Resources.LoadAll<Theme>("Themes");
        if (loadedThemes != null && loadedThemes.Length > 0)
        {
            availableThemes = loadedThemes;
            if (currentTheme == null)
            {
                currentTheme = availableThemes[0];
            }
        }
    }
    [SerializeField] private Theme currentTheme;
    private TileBoard tileBoard;

    private void Awake()
    {
        if (instance != null && instance != this)
        {
            Destroy(gameObject);
            return;
        }

        instance = this;
        DontDestroyOnLoad(gameObject);
        
        // 从Resources/Themes目录加载所有主题
        LoadThemesFromResources();
    }

    public void Initialize(TileBoard board)
    {
        tileBoard = board;
        if (currentTheme == null && availableThemes != null && availableThemes.Length > 0)
        {
            currentTheme = availableThemes[0];
        }
        ApplyCurrentTheme();
    }

    public void SetTheme(Theme theme)
    {
        if (theme != null && theme != currentTheme)
        {
            currentTheme = theme;
            ApplyCurrentTheme();
        }
    }

    public Theme GetCurrentTheme()
    {
        return currentTheme;
    }

    public Theme[] GetAvailableThemes()
    {
        return availableThemes;
    }

    private void ApplyCurrentTheme()
    {
        if (currentTheme == null || tileBoard == null) return;

        // 应用棋盘背景色
        tileBoard.SetBoardColor(currentTheme.boardBackgroundColor);

        // 更新所有砖块的颜色
        tileBoard.UpdateTileColors(currentTheme);
    }
}