using UnityEngine;
using System.Collections.Generic;
using System;
using System.Collections;

public class MusicManager : MonoBehaviour
{
    private static MusicManager instance;
    public static MusicManager Instance
    {
        get
        {
            if (instance == null)
            {
                GameObject go = new GameObject("MusicManager");
                instance = go.AddComponent<MusicManager>();
                DontDestroyOnLoad(go);
            }
            return instance;
        }
    }

    // 音频源组件
    private AudioSource audioSource;

    // 当前激活的乐器音效
    private Dictionary<string, AudioClip> currentInstrumentClips = new Dictionary<string, AudioClip>();

    // 乐谱数据结构
    [System.Serializable]
    public class MusicScore
    {
        public string name;
        public string[] notes; // 音符序列，对应音效文件名
    }

    // 所有可用乐谱
    public List<MusicScore> musicScores = new List<MusicScore>();

    // 预定义的乐谱
    private void InitializeDefaultScores()
    {
        // 莫扎特《小星星变奏曲》主题与第一变奏
        MusicScore twinkle = new MusicScore
        {
            name = "Twinkle Twinkle Little Star",
            notes = new string[] { 
                // 主题
                "C4","C4","G4","G4","A4","A4","G4", 
                "F4","F4","E4","E4","D4","D4","C4",
                "G4","G4","F4","F4","E4","E4","D4",
                "G4","G4","F4","F4","E4","E4","C4",
                // 第一变奏
                "C4","E4","G4","C5","G4","E4","C4",
                "F4","A4","C5","F5","C5","A4","F4",
                "E4","G4","C5","E5","C5","G4","E4",
                "C4","E4","G4","C5","G4","E4","C4"
            }
        };

        // 巴赫《小步舞曲》完整第一乐章
        MusicScore minuet = new MusicScore
        {
            name = "Minuet in G",
            notes = new string[] { 
                // A段
                "G4","D4","G4","A4","B4","C5","D5",
                "G4","D4","G4","A4","B4","C5","D5",
                "E5","C5","D5","B4","C5","A4","B4",
                "G4","A4","B4","A4","G4","F#4","G4",
                // B段
                "D5","G4","A4","B4","C5","D5","E5",
                "D5","G4","A4","B4","C5","D5","E5",
                "F5","D5","E5","C5","D5","B4","C5",
                "A4","B4","C5","B4","A4","G4","A4",
                // 返回A段
                "G4","D4","G4","A4","B4","C5","D5"
            }
        };

        // 贝多芬《致爱丽丝》A段和B段
        MusicScore elise = new MusicScore
        {
            name = "Fur Elise",
            notes = new string[] { 
                // A段
                "E5","D#5","E5","D#5","E5","B4","D5","C5","A4",
                "C4","E4","A4","B4","E4","G#4","B4","C5",
                "E4","E5","D#5","E5","D#5","E5","B4","D5","C5","A4",
                // B段
                "C4","E4","A4","B4","E4","C5","B4","A4",
                "B4","C5","D5","E5","G4","F5","E5","D5",
                "F4","E5","D5","C5","E4","D5","C5","B4",
                // 返回A段
                "E4","E5","D#5","E5","D#5","E5","B4","D5","C5","A4"
            }
        };

        // 添加乐谱到列表
        musicScores.Add(twinkle);
        musicScores.Add(minuet);
        musicScores.Add(elise);

        // 设置小星星变奏曲为当前乐谱
        SetCurrentScore(twinkle);
    }

    // 当前激活的乐谱
    private MusicScore currentScore;
    private int currentNoteIndex = 0;

    private void Awake()
    {
        if (instance != null && instance != this)
        {
            Destroy(gameObject);
            return;
        }

        instance = this;
        DontDestroyOnLoad(gameObject);

        // 初始化音频源
        audioSource = gameObject.AddComponent<AudioSource>();

        // 初始化默认乐谱
        InitializeDefaultScores();
    }

    // 加载乐器音效
    public void LoadInstrument(string instrumentName)
    {
        currentInstrumentClips.Clear();
        // 从Resources文件夹加载音效
        AudioClip[] clips = Resources.LoadAll<AudioClip>($"Instruments/{instrumentName}");
        foreach (AudioClip clip in clips)
        {
            currentInstrumentClips[clip.name] = clip;
        }
    }

    // 添加乐谱
    public void AddMusicScore(MusicScore score)
    {
        // 验证音符格式
        foreach (string note in score.notes)
        {
            if (string.IsNullOrEmpty(note) || !"ABCDEFG".Contains(note.ToUpper()))
            {
                Debug.LogError($"无效的音符格式: {note}，乐谱 {score.name} 未被添加");
                return;
            }
        }

        musicScores.Add(score);
        if (currentScore == null)
        {
            SetCurrentScore(score);
        }
    }

    // 设置当前乐谱
    public void SetCurrentScore(MusicScore score)
    {
        currentScore = score;
        currentNoteIndex = 0;
    }

    // 获取当前乐谱剩余音节数量
    public int GetRemainingNotes()
    {
        if (currentScore == null) return 0;
        return currentScore.notes.Length - currentNoteIndex;
    }

    // 获取当前乐谱最后的N个音节
    private string[] GetLastNNotes(int count)
    {
        if (currentScore == null || count <= 0) return new string[0];
        int startIndex = Math.Max(0, currentScore.notes.Length - count);
        int actualCount = Math.Min(count, currentScore.notes.Length);
        string[] lastNotes = new string[actualCount];
        Array.Copy(currentScore.notes, startIndex, lastNotes, 0, actualCount);
        return lastNotes;
    }

    // 播放指定数量的音节
    public IEnumerator PlayNNotesCoroutine(int count)
    {
        if (currentScore == null || count <= 0) yield break;

        string[] notesToPlay;
        int remainingNotes = GetRemainingNotes();

        if (remainingNotes > count)
        {
            // 播放后续count个音节
            notesToPlay = new string[count];
            Array.Copy(currentScore.notes, currentNoteIndex, notesToPlay, 0, count);
        }
        else
        {
            // 播放最后count个音节
            notesToPlay = GetLastNNotes(count);
        }

        foreach (string note in notesToPlay)
        {
            if (currentInstrumentClips.ContainsKey(note))
            {
                audioSource.PlayOneShot(currentInstrumentClips[note]);
                // 使用协程等待，避免阻塞主线程
                yield return new WaitForSeconds(0.2f);
            }
        }
    }

    // 播放指定数量的音节（非协程版本，用于向后兼容）
    public void PlayNNotes(int count)
    {
        StartCoroutine(PlayNNotesCoroutine(count));
    }

    // 播放下一个音符
    public void PlayNextNote()
    {
        if (currentScore == null || currentScore.notes.Length == 0) return;

        string noteName = currentScore.notes[currentNoteIndex];
        if (currentInstrumentClips.ContainsKey(noteName))
        {
            audioSource.PlayOneShot(currentInstrumentClips[noteName]);
        }

        currentNoteIndex++;
        if (currentNoteIndex >= currentScore.notes.Length)
        {
            // 乐谱播放完毕，随机选择下一个乐谱
            SelectRandomScore();
        }
    }

    // 随机选择下一个乐谱
    private void SelectRandomScore()
    {
        if (musicScores.Count <= 1) return;

        int randomIndex;
        do
        {
            randomIndex = UnityEngine.Random.Range(0, musicScores.Count);
        } while (musicScores[randomIndex] == currentScore);

        SetCurrentScore(musicScores[randomIndex]);
        
        // 确保音效资源已加载
        if (currentInstrumentClips.Count == 0)
        {
            LoadInstrument("Piano"); // 默认使用钢琴音色
        }
        
        // 验证所有音符的音效是否存在
        foreach (string note in currentScore.notes)
        {
            if (!currentInstrumentClips.ContainsKey(note))
            {
                Debug.LogWarning($"音符 {note} 的音效资源未找到");
            }
        }
    }
}