像素艺术,头像，武将和铠甲和头盔和红缨，男性，游戏，只有一个，三国时期，
唐朝，宋朝，古代，冷兵器

游戏地图
我在用godot引擎4.3版本开发游戏，使用gdscript语言，
现在帮我在这个场景中初始化一张瓦片地图，尺寸是100*100，场景上有两个按钮，分别是放大和缩小，
可以缩放场景地图显示，瓦片地图具有lod的能力，此外，鼠标拖拽可以移动地图

好了，现在 地图出来了，现在 帮我在 地图上的 随机两个坐标 生成 各一个士兵，分别是红色和蓝色阵营的士兵。
士兵有 5 HP，攻击1，移动能力3。点击士兵 后，士兵 进入激活状态，此时出现 新的 ui 按钮，分别是 移动，攻击，待机，三个按钮。
点击移动按钮，在这个 士兵 周围 生成 移动能力 相等的 绿色 区块，点击区块的 任一 格点，士兵就会 移动过去，此时，该士兵结束行动，
处于置灰状态。点击攻击 按钮，会在 该士兵 四周 一格 区域产生红色 格点，点击 某一 格点，如果 该格点中 有 敌对阵营的士兵，就发动攻击，
敌对阵营的 该士兵的 HP 减少 己方 攻击力的 数值，如果，HP 小于等于 0，士兵就 消失。如果 同一时间 只剩 一方 阵营，则该阵营胜利，弹出 弹窗，可以点击 重新开局。




midjounery：
主体+状态+背景+氛围+风格
ui：
The game interface design features a European castle background image with magic elements and a cartoon style UI for character equipment
in the middle of the screen. The left hand panel displays ability icons in a table with a "circlet" on top. 
The right side features a grid icon display area showing crystal diamond gems with small round white boxes at the bottom. 
It has a top down view. In the style of a Disney animated movie like Frozen, the background features a cracked ice surface and foggy blue 
sky to create a dreamy atmosphere through exquisite 3D rendering details in full color at a high resolution





一张干净的图集，展示了一个攻击状态的序列帧，每帧动画逐步展示，角色是三国时期的重甲步兵，左手持盾右手持刀。每一帧展示了攻击动画的不同状态，从站立，挥砍到结束，突出攻击动画，
帧与帧之间间隔均等，像素游戏风格。背景为白色，干净有序的画面



