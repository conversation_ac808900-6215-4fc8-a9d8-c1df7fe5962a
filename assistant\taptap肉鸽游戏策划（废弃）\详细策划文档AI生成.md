# 🎮 肉鸽自走棋游戏详细策划文档

## 📋 项目概述

### 游戏名称
**《元素守护者：无尽征途》**

### 游戏类型
肉鸽自走棋 + 宠物养成 + 策略战斗

### 核心玩法
- 杀戮尖塔式路线选择
- 自走棋战斗系统
- 宠物收集与养成
- 装备合成与强化

### 技术栈
- **前端引擎**: PixiJS
- **框架**: React (Web版本)
- **状态管理**: Redux Toolkit
- **数据存储**: LocalStorage + IndexedDB
- **音效**: Howler.js

---

## 🎯 核心系统设计

### 1. 英雄系统

#### 英雄属性
```javascript
const heroAttributes = {
  hp: 100,           // 生命值
  attack: 10,        // 攻击力
  defense: 5,        // 防御力
  speed: 8,          // 速度
  mana: 50,          // 法力值
  level: 1,          // 等级
  experience: 0      // 经验值
}
```

#### 英雄技能分类
- **主动技能**: 消耗法力值释放
- **被动技能**: 永久生效
- **队长技能**: 影响全队的光环效果
- **觉醒技能**: 达到特定条件解锁

#### 双英雄组队机制
- 主英雄：队长，可在大地图使用技能
- 副英雄：提供被动加成和支援技能
- 组合技：特定英雄搭配解锁强力技能

### 2. 随从（宠物）系统

#### 随从分类
```javascript
const companionTypes = {
  ELEMENTAL: "元素系",    // 火、水、土、风、雷、冰、光、暗
  BEAST: "野兽系",       // 狼、熊、鹰、蛇等
  MECHANICAL: "机械系",   // 齿轮、蒸汽、魔导等
  UNDEAD: "亡灵系",      // 骷髅、幽灵、僵尸等
  DRAGON: "龙族系",      // 各种龙类
  PLANT: "植物系",       // 树精、花妖等
  SPIRIT: "精灵系",      // 光精灵、暗精灵等
  CHAOS: "混沌系"        // 特殊变异生物
}
```

#### 随从稀有度
- **普通** (白色): 70% 出现率
- **稀有** (绿色): 20% 出现率
- **史诗** (蓝色): 8% 出现率
- **传说** (紫色): 1.8% 出现率
- **神话** (金色): 0.2% 出现率

#### 随从属性与成长
```javascript
const companionStats = {
  baseStats: {
    hp: [50, 200],        // 生命值范围
    attack: [5, 50],      // 攻击力范围
    defense: [2, 30],     // 防御力范围
    speed: [3, 15],       // 速度范围
    cost: [1, 8]          // 部署费用
  },
  growthRates: {
    hp: [2, 8],           // 每级成长
    attack: [1, 3],
    defense: [0.5, 2],
    speed: [0.2, 0.8]
  }
}
```

#### 随从技能系统
- **种族技能**: 同种族随从的协同效果
- **元素技能**: 元素克制与组合
- **进化技能**: 达到条件后获得新技能
- **装备技能**: 通过装备获得的技能

### 3. 战斗系统

#### 自走棋机制
- **8x8棋盘**: 标准战斗区域
- **回合制**: 准备阶段 + 战斗阶段
- **自动战斗**: AI控制随从行动
- **策略部署**: 玩家控制阵型和技能释放时机

#### 战斗流程
1. **准备阶段** (30秒)
   - 购买/出售随从
   - 调整阵型
   - 使用道具
   - 升级随从

2. **战斗阶段** (自动)
   - 随从按速度顺序行动
   - 技能自动释放
   - 英雄技能手动释放
   - 战斗结算

#### 胜负条件
- 消灭对方所有随从
- 对方英雄生命值归零
- 时间结束按剩余生命值判定

### 4. 肉鸽地牢系统

#### 地图生成
```javascript
const dungeonStructure = {
  floors: 15,           // 总层数
  roomTypes: {
    BATTLE: 0.6,        // 战斗房间 60%
    SHOP: 0.15,         // 商店房间 15%
    TREASURE: 0.1,      // 宝藏房间 10%
    EVENT: 0.1,         // 事件房间 10%
    BOSS: 0.05          // Boss房间 5%
  },
  pathChoices: 3        // 每次可选择3条路径
}
```

#### 房间类型详解

**战斗房间**
- 随机敌人配置
- 胜利奖励：金币、经验、随从
- 失败惩罚：生命值损失

**商店房间**
- 随从购买/出售
- 装备购买/强化
- 道具购买
- 刷新商店（消耗金币）

**宝藏房间**
- 稀有装备
- 特殊道具
- 随从进化材料
- 可能有守护者需要战斗

**事件房间**
- 随机事件选择
- 风险与收益并存
- 影响后续游戏进程

**Boss房间**
- 强力Boss战斗
- 丰厚奖励
- 解锁新内容

### 5. 装备与道具系统

#### 装备分类
```javascript
const equipmentTypes = {
  WEAPON: "武器",        // 增加攻击力
  ARMOR: "护甲",         // 增加防御力和生命值
  ACCESSORY: "饰品",     // 提供特殊效果
  CONSUMABLE: "消耗品"   // 一次性使用道具
}
```

#### 装备合成
- **基础材料**: 在地牢中获得
- **合成公式**: 固定配方 + 随机配方
- **强化系统**: 消耗材料提升装备等级
- **附魔系统**: 添加随机属性

#### 道具效果示例
```javascript
const itemEffects = {
  "火焰宝石": {
    effect: "所有火系随从攻击力+20%",
    rarity: "稀有",
    stackable: false
  },
  "生命药水": {
    effect: "立即恢复50%生命值",
    rarity: "普通",
    stackable: true,
    maxStack: 5
  },
  "进化石": {
    effect: "随机一个随从获得新技能",
    rarity: "史诗",
    stackable: false
  }
}
```

---

## 🎨 美术资源需求

### 角色设计
- **英雄**: 20个不同职业的英雄角色
- **随从**: 200个不同的宠物/生物
- **Boss**: 15个独特的Boss设计

### 场景设计
- **地牢环境**: 5种不同主题的地牢场景
- **战斗场地**: 多种地形效果的棋盘
- **UI界面**: 完整的游戏界面设计

### 特效设计
- **技能特效**: 每个技能的视觉效果
- **战斗特效**: 攻击、防御、治疗等效果
- **环境特效**: 地牢氛围营造

---

## 🔧 技术实现要点

### 核心架构
```javascript
// 游戏状态管理
const gameState = {
  player: {
    heroes: [],
    companions: [],
    inventory: [],
    currency: 0,
    achievements: []
  },
  dungeon: {
    currentFloor: 1,
    currentRoom: null,
    availablePaths: [],
    completedRooms: []
  },
  battle: {
    playerTeam: [],
    enemyTeam: [],
    battlefield: [],
    turnOrder: [],
    currentTurn: 0
  }
}
```

### 关键算法
1. **随机生成算法**: 地牢、敌人、奖励的随机生成
2. **AI战斗算法**: 随从的自动战斗逻辑
3. **平衡性算法**: 难度曲线和数值平衡
4. **存档系统**: 游戏进度的保存和加载

### 性能优化
- **对象池**: 复用游戏对象减少GC
- **纹理图集**: 合并小图片减少绘制调用
- **LOD系统**: 根据距离调整渲染质量
- **异步加载**: 分批加载资源避免卡顿

---

## 📊 数据设计

### 随从数据结构
```javascript
const companionData = {
  id: "fire_dragon_001",
  name: "烈焰幼龙",
  type: "DRAGON",
  element: "FIRE",
  rarity: "EPIC",
  baseStats: {
    hp: 120,
    attack: 25,
    defense: 15,
    speed: 8,
    cost: 4
  },
  skills: [
    {
      id: "flame_breath",
      name: "烈焰吐息",
      type: "ACTIVE",
      cooldown: 3,
      effect: "对前方3格敌人造成150%攻击力的火焰伤害"
    }
  ],
  evolution: {
    materials: ["dragon_scale", "fire_essence"],
    result: "fire_dragon_002"
  }
}
```

### 关卡数据结构
```javascript
const levelData = {
  id: "dungeon_1_3",
  floor: 1,
  roomType: "BATTLE",
  enemies: [
    {
      companionId: "goblin_warrior",
      level: 2,
      position: [2, 1]
    }
  ],
  rewards: {
    gold: [50, 100],
    experience: [20, 40],
    items: ["health_potion", "fire_gem"]
  },
  difficulty: 1.2
}
```

---

## 🎯 游戏平衡性

### 难度曲线
- **前期** (1-5层): 教学和适应期，难度较低
- **中期** (6-10层): 策略性增强，需要合理搭配
- **后期** (11-15层): 高难度挑战，考验玩家掌握程度

### 经济平衡
- **收入来源**: 战斗奖励、出售随从、完成成就
- **支出项目**: 购买随从、升级、道具、刷新商店
- **通胀控制**: 后期收入和支出同步增长

### 随从平衡
- **稀有度平衡**: 高稀有度随从强度更高但获取困难
- **种族平衡**: 每个种族都有独特优势和劣势
- **成本平衡**: 强力随从需要更高的部署成本

---

## 🏆 成就与统计系统

### 成就分类
```javascript
const achievements = {
  COLLECTION: [
    {
      id: "collector_novice",
      name: "初级收集家",
      description: "收集10种不同的随从",
      reward: "gold:500"
    }
  ],
  BATTLE: [
    {
      id: "perfect_victory",
      name: "完美胜利",
      description: "在一场战斗中无伤获胜",
      reward: "title:完美战士"
    }
  ],
  PROGRESSION: [
    {
      id: "dungeon_master",
      name: "地牢大师",
      description: "完成100次地牢探险",
      reward: "companion:legendary_phoenix"
    }
  ]
}
```

### 统计数据
- **游戏时长**: 总游戏时间和单局时间
- **胜率统计**: 各种模式的胜率
- **收集进度**: 随从、装备、成就的收集率
- **战斗数据**: 造成伤害、承受伤害、击杀数等

---

## 🚀 开发里程碑

### 第一阶段 (MVP)
- [ ] 基础战斗系统
- [ ] 简单的随从系统 (20种随从)
- [ ] 基础地牢探险
- [ ] 核心UI界面

### 第二阶段 (Alpha)
- [ ] 完整随从系统 (100种随从)
- [ ] 装备与道具系统
- [ ] 成就系统
- [ ] 音效与特效

### 第三阶段 (Beta)
- [ ] 完整随从库 (200种随从)
- [ ] 多种游戏模式
- [ ] 平衡性调优
- [ ] 性能优化

### 第四阶段 (Release)
- [ ] 最终测试
- [ ] 发布准备
- [ ] 运营支持

---

## 💡 创新特色

1. **动态组合系统**: 随从之间的实时组合效果
2. **环境互动**: 战场地形影响战斗策略
3. **记忆系统**: AI学习玩家习惯调整难度
4. **社交元素**: 分享自己的随从搭配和战术

---

## 📝 AI实现提示

### 核心提示词
```
请基于以上详细策划文档，使用PixiJS实现一个完整的肉鸽自走棋游戏。

重点实现：
1. 模块化的代码架构，便于扩展
2. 完整的游戏循环和状态管理
3. 直观的UI界面和流畅的动画
4. 平衡的游戏数值和有趣的玩法
5. 可扩展的数据结构，支持后续内容添加

请确保游戏具有完整的可玩性，包括教程、存档、设置等基础功能。
```

---

---

## 🎮 具体实现指南

### 项目结构
```
src/
├── core/                 # 核心系统
│   ├── GameEngine.js    # 游戏引擎主类
│   ├── StateManager.js  # 状态管理
│   ├── EventSystem.js   # 事件系统
│   └── SaveSystem.js    # 存档系统
├── systems/             # 游戏系统
│   ├── BattleSystem.js  # 战斗系统
│   ├── DungeonSystem.js # 地牢系统
│   ├── CompanionSystem.js # 随从系统
│   └── InventorySystem.js # 背包系统
├── entities/            # 游戏实体
│   ├── Hero.js         # 英雄类
│   ├── Companion.js    # 随从类
│   ├── Item.js         # 道具类
│   └── Skill.js        # 技能类
├── ui/                  # 用户界面
│   ├── MainMenu.js     # 主菜单
│   ├── BattleUI.js     # 战斗界面
│   ├── InventoryUI.js  # 背包界面
│   └── ShopUI.js       # 商店界面
├── data/                # 游戏数据
│   ├── companions.json # 随从数据
│   ├── skills.json     # 技能数据
│   ├── items.json      # 道具数据
│   └── levels.json     # 关卡数据
├── assets/              # 游戏资源
│   ├── sprites/        # 精灵图片
│   ├── sounds/         # 音效文件
│   └── fonts/          # 字体文件
└── utils/               # 工具函数
    ├── MathUtils.js    # 数学工具
    ├── RandomUtils.js  # 随机工具
    └── AnimationUtils.js # 动画工具
```

### 核心代码示例

#### 游戏引擎主类
```javascript
class GameEngine {
  constructor() {
    this.app = new PIXI.Application({
      width: 1280,
      height: 720,
      backgroundColor: 0x1099bb
    });

    this.stateManager = new StateManager();
    this.eventSystem = new EventSystem();
    this.saveSystem = new SaveSystem();

    this.init();
  }

  init() {
    // 初始化游戏系统
    this.battleSystem = new BattleSystem(this);
    this.dungeonSystem = new DungeonSystem(this);
    this.companionSystem = new CompanionSystem(this);

    // 加载资源
    this.loadAssets();

    // 启动游戏循环
    this.app.ticker.add(this.update.bind(this));
  }

  update(delta) {
    // 更新所有系统
    this.stateManager.update(delta);
    this.battleSystem.update(delta);
    this.dungeonSystem.update(delta);
  }
}
```

#### 随从类实现
```javascript
class Companion {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.type = data.type;
    this.element = data.element;
    this.rarity = data.rarity;

    // 属性
    this.level = 1;
    this.experience = 0;
    this.stats = { ...data.baseStats };
    this.maxStats = { ...data.baseStats };

    // 技能
    this.skills = data.skills.map(skill => new Skill(skill));

    // 状态
    this.position = { x: 0, y: 0 };
    this.isAlive = true;
    this.statusEffects = [];

    // 视觉
    this.sprite = new PIXI.Sprite();
    this.loadSprite(data.spriteUrl);
  }

  // 升级
  levelUp() {
    this.level++;
    this.stats.hp += this.getGrowthRate('hp');
    this.stats.attack += this.getGrowthRate('attack');
    this.stats.defense += this.getGrowthRate('defense');
    this.updateMaxStats();
  }

  // 使用技能
  useSkill(skillIndex, target) {
    const skill = this.skills[skillIndex];
    if (skill && skill.canUse()) {
      return skill.execute(this, target);
    }
    return false;
  }

  // 受到伤害
  takeDamage(damage, type = 'physical') {
    const actualDamage = this.calculateDamage(damage, type);
    this.stats.hp = Math.max(0, this.stats.hp - actualDamage);

    if (this.stats.hp <= 0) {
      this.die();
    }

    return actualDamage;
  }

  // 计算伤害
  calculateDamage(damage, type) {
    let actualDamage = damage;

    // 防御减伤
    if (type === 'physical') {
      actualDamage = Math.max(1, damage - this.stats.defense);
    }

    // 元素克制
    actualDamage *= this.getElementalModifier(type);

    // 状态效果影响
    actualDamage *= this.getStatusModifier();

    return Math.floor(actualDamage);
  }
}
```

#### 战斗系统核心
```javascript
class BattleSystem {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.battlefield = new Array(8).fill(null).map(() => new Array(8).fill(null));
    this.turnQueue = [];
    this.currentTurn = 0;
    this.battlePhase = 'PREPARATION'; // PREPARATION, BATTLE, RESULT
  }

  // 开始战斗
  startBattle(playerTeam, enemyTeam) {
    this.playerTeam = playerTeam;
    this.enemyTeam = enemyTeam;

    // 部署随从到战场
    this.deployTeam(playerTeam, 'player');
    this.deployTeam(enemyTeam, 'enemy');

    // 计算行动顺序
    this.calculateTurnOrder();

    this.battlePhase = 'BATTLE';
    this.processTurn();
  }

  // 处理回合
  processTurn() {
    if (this.turnQueue.length === 0) {
      this.endRound();
      return;
    }

    const currentUnit = this.turnQueue.shift();
    if (currentUnit.isAlive) {
      this.executeUnitAction(currentUnit);
    }

    // 检查战斗结束条件
    if (this.checkBattleEnd()) {
      this.endBattle();
    } else {
      // 延迟处理下一个单位
      setTimeout(() => this.processTurn(), 500);
    }
  }

  // 执行单位行动
  executeUnitAction(unit) {
    // AI决策或玩家输入
    const action = this.getUnitAction(unit);

    switch (action.type) {
      case 'ATTACK':
        this.executeAttack(unit, action.target);
        break;
      case 'SKILL':
        this.executeSkill(unit, action.skillIndex, action.target);
        break;
      case 'MOVE':
        this.executeMove(unit, action.position);
        break;
      case 'WAIT':
        // 等待，不做任何操作
        break;
    }
  }

  // 获取单位行动（AI逻辑）
  getUnitAction(unit) {
    // 简单AI逻辑
    const enemies = this.getEnemies(unit);
    const nearestEnemy = this.findNearestEnemy(unit, enemies);

    if (nearestEnemy) {
      const distance = this.getDistance(unit.position, nearestEnemy.position);

      // 如果在攻击范围内，攻击
      if (distance <= unit.attackRange) {
        return {
          type: 'ATTACK',
          target: nearestEnemy
        };
      }

      // 如果有可用技能，考虑使用
      const availableSkill = unit.skills.find(skill => skill.canUse());
      if (availableSkill && Math.random() < 0.3) {
        return {
          type: 'SKILL',
          skillIndex: unit.skills.indexOf(availableSkill),
          target: nearestEnemy
        };
      }

      // 否则移动靠近敌人
      const movePosition = this.findMovePosition(unit, nearestEnemy);
      return {
        type: 'MOVE',
        position: movePosition
      };
    }

    return { type: 'WAIT' };
  }
}
```

### 数据配置示例

#### 随从数据配置
```json
{
  "companions": [
    {
      "id": "fire_imp",
      "name": "火焰小鬼",
      "type": "ELEMENTAL",
      "element": "FIRE",
      "rarity": "COMMON",
      "baseStats": {
        "hp": 60,
        "attack": 15,
        "defense": 8,
        "speed": 12,
        "cost": 2
      },
      "growthRates": {
        "hp": 3,
        "attack": 1.5,
        "defense": 1,
        "speed": 0.5
      },
      "skills": [
        {
          "id": "fireball",
          "name": "火球术",
          "type": "ACTIVE",
          "cooldown": 2,
          "manaCost": 10,
          "range": 3,
          "effect": {
            "type": "DAMAGE",
            "value": 1.5,
            "element": "FIRE",
            "aoe": false
          }
        }
      ],
      "spriteUrl": "sprites/companions/fire_imp.png",
      "description": "来自火元素位面的小恶魔，擅长火焰魔法。"
    }
  ]
}
```

#### 技能效果系统
```javascript
class SkillEffect {
  static effects = {
    DAMAGE: (caster, target, params) => {
      const damage = caster.stats.attack * params.value;
      return target.takeDamage(damage, params.element);
    },

    HEAL: (caster, target, params) => {
      const healAmount = caster.stats.attack * params.value;
      target.heal(healAmount);
      return healAmount;
    },

    BUFF: (caster, target, params) => {
      target.addStatusEffect({
        type: params.buffType,
        value: params.value,
        duration: params.duration
      });
    },

    SUMMON: (caster, target, params) => {
      const summonData = GameData.companions[params.companionId];
      const summon = new Companion(summonData);
      // 在战场上召唤
      this.battlefield.addUnit(summon, params.position);
    }
  };

  static execute(effectType, caster, target, params) {
    if (this.effects[effectType]) {
      return this.effects[effectType](caster, target, params);
    }
  }
}
```

### UI实现要点

#### 响应式布局
```javascript
class ResponsiveUI {
  constructor(app) {
    this.app = app;
    this.baseWidth = 1280;
    this.baseHeight = 720;
    this.scale = 1;

    this.setupResponsive();
  }

  setupResponsive() {
    window.addEventListener('resize', () => {
      this.updateScale();
    });

    this.updateScale();
  }

  updateScale() {
    const scaleX = window.innerWidth / this.baseWidth;
    const scaleY = window.innerHeight / this.baseHeight;
    this.scale = Math.min(scaleX, scaleY);

    this.app.stage.scale.set(this.scale);
    this.app.renderer.resize(
      this.baseWidth * this.scale,
      this.baseHeight * this.scale
    );
  }
}
```

#### 动画系统
```javascript
class AnimationManager {
  static tweens = [];

  static to(target, duration, properties) {
    const tween = {
      target,
      duration,
      properties,
      startTime: Date.now(),
      startValues: {}
    };

    // 记录初始值
    for (const prop in properties) {
      tween.startValues[prop] = target[prop];
    }

    this.tweens.push(tween);
    return tween;
  }

  static update() {
    const now = Date.now();

    this.tweens = this.tweens.filter(tween => {
      const elapsed = now - tween.startTime;
      const progress = Math.min(elapsed / tween.duration, 1);

      // 更新属性值
      for (const prop in tween.properties) {
        const start = tween.startValues[prop];
        const end = tween.properties[prop];
        tween.target[prop] = start + (end - start) * progress;
      }

      // 如果动画完成，从列表中移除
      return progress < 1;
    });
  }
}
```

---

## 🎯 AI实现建议

### 分步实现策略

1. **第一步：基础框架**
   - 搭建PixiJS项目结构
   - 实现基础的游戏循环
   - 创建简单的状态管理

2. **第二步：核心实体**
   - 实现Hero和Companion类
   - 基础的属性和技能系统
   - 简单的战斗逻辑

3. **第三步：战斗系统**
   - 8x8棋盘战场
   - 回合制战斗流程
   - AI行为逻辑

4. **第四步：地牢系统**
   - 房间生成和连接
   - 路径选择机制
   - 奖励和事件系统

5. **第五步：UI和交互**
   - 游戏界面设计
   - 用户交互逻辑
   - 动画和特效

6. **第六步：数据和平衡**
   - 完善游戏数据
   - 平衡性调整
   - 存档系统

### 关键技术点

- **性能优化**: 使用对象池、纹理图集、LOD系统
- **数据驱动**: 所有游戏内容通过JSON配置
- **模块化设计**: 便于扩展和维护
- **错误处理**: 完善的异常处理机制

---

*本文档为《元素守护者：无尽征途》的完整策划和实现指南，为AI开发提供详细的技术方案。*
