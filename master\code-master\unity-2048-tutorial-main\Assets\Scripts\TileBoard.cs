using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SocialPlatforms;
using UnityEngine.UI;

public class TileBoard : MonoBehaviour
{
    [SerializeField] private Tile tilePrefab;
    [SerializeField] private TileState[] tileStates;
    [SerializeField] private Transform boardBackgroundT;
    private Image boardBackground;

    private TileGrid grid;
    private List<Tile> tiles;
    private bool waiting;

    private Vector2 touchStartPos;
    private bool isSwiping;
    private const float SwipeThreshold = 20f; // 降低滑动判定阈值以提高响应速度

    private MusicManager musicManager;

    private void Awake()
    {
        grid = GetComponentInChildren<TileGrid>();
        tiles = new List<Tile>(16);
        musicManager = MusicManager.Instance;
        boardBackground = boardBackgroundT.GetComponent<Image>();
        ThemeManager.Instance.Initialize(this);
    }

    public void ClearBoard()
    {
        foreach (var cell in grid.cells) {
            cell.tile = null;
        }

        foreach (var tile in tiles) {
            Destroy(tile.gameObject);
        }

        tiles.Clear();
    }

    public void CreateTile()
    {
        Tile tile = Instantiate(tilePrefab, grid.transform);
        // 80%概率生成2，20%概率生成4
        int stateIndex = Random.Range(0f, 1f) < 0.8f ? 0 : 1;
        tile.SetState(tileStates[stateIndex]);
        tile.Spawn(grid.GetRandomEmptyCell());
        tiles.Add(tile);

        // 应用当前主题的颜色
        Theme currentTheme = ThemeManager.Instance.GetCurrentTheme();
        if (currentTheme != null)
        {
            TileColorConfig config = currentTheme.GetTileConfig(tile.state.number);
            if (config != null)
            {
                tile.state.backgroundColor = config.backgroundColor;
                tile.state.textColor = config.textColor;
            }
        }
    }

    public void SetBoardColor(Color color)
    {
        
        if (boardBackground != null)
        {
            boardBackground.color = color;
        }
    }

    public void UpdateTileColors(Theme theme)
    {
        if (theme == null) return;

        foreach (var tile in tiles)
        {
            int index = (int)System.Math.Log(tile.state.number, 2);


            tile.SetState(tileStates[index]);


            //TileColorConfig config = theme.GetTileConfig(tile.state.number);
            //if (config != null)
            //{
            //    tile.state.backgroundColor = config.backgroundColor;
            //    tile.state.textColor = config.textColor;
            //}
        }
    }

    private void Update()
    {
        if (waiting) return;

        // ԭ�м��̿����߼�
        HandleKeyboardInput();

        // �����ƶ��˻�������
        HandleTouchInput();

        //if (Input.GetKeyDown(KeyCode.W) || Input.GetKeyDown(KeyCode.UpArrow)) {
        //    Move(Vector2Int.up, 0, 1, 1, 1);
        //} else if (Input.GetKeyDown(KeyCode.A) || Input.GetKeyDown(KeyCode.LeftArrow)) {
        //    Move(Vector2Int.left, 1, 1, 0, 1);
        //} else if (Input.GetKeyDown(KeyCode.S) || Input.GetKeyDown(KeyCode.DownArrow)) {
        //    Move(Vector2Int.down, 0, 1, grid.Height - 2, -1);
        //} else if (Input.GetKeyDown(KeyCode.D) || Input.GetKeyDown(KeyCode.RightArrow)) {
        //    Move(Vector2Int.right, grid.Width - 2, -1, 0, 1);
        //}
    }


    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(KeyCode.W) || Input.GetKeyDown(KeyCode.UpArrow))
        {
            Move(Vector2Int.up, 0, 1, 1, 1);
        }
        else if (Input.GetKeyDown(KeyCode.A) || Input.GetKeyDown(KeyCode.LeftArrow))
        {
            Move(Vector2Int.left, 1, 1, 0, 1);
        }
        else if (Input.GetKeyDown(KeyCode.S) || Input.GetKeyDown(KeyCode.DownArrow))
        {
            Move(Vector2Int.down, 0, 1, grid.Height - 2, -1);
        }
        else if (Input.GetKeyDown(KeyCode.D) || Input.GetKeyDown(KeyCode.RightArrow))
        {
            Move(Vector2Int.right, grid.Width - 2, -1, 0, 1);
        }
    }

    private void HandleTouchInput()
    {
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);

            switch (touch.phase)
            {
                case TouchPhase.Began:
                    touchStartPos = touch.position;
                    isSwiping = true;
                    break;

                case TouchPhase.Moved:
                    if (isSwiping)
                    {
                        Vector2 swipeDelta = touch.position - touchStartPos;
                        if (swipeDelta.magnitude >= SwipeThreshold)
                        {
                            ProcessSwipe(swipeDelta);
                            isSwiping = false;
                        }
                    }
                    break;

                case TouchPhase.Ended:
                    isSwiping = false;
                    break;
            }
        }
    }

    private void ProcessSwipe(Vector2 delta)
    {
        if (delta.magnitude < SwipeThreshold) return;

        float horizontalRatio = Mathf.Abs(delta.x / Screen.width);
        float verticalRatio = Mathf.Abs(delta.y / Screen.height);

        // �����жϽϴ�λ�Ʒ���
        if (horizontalRatio > verticalRatio)
        {
            if (delta.x > 0) // �һ�
            {
                Move(Vector2Int.right, grid.Width - 2, -1, 0, 1);
            }
            else // ��
            {
                Move(Vector2Int.left, 1, 1, 0, 1);
            }
        }
        else
        {
            if (delta.y > 0) // �ϻ�
            {
                Move(Vector2Int.up, 0, 1, 1, 1);
            }
            else // �»�
            {
                Move(Vector2Int.down, 0, 1, grid.Height - 2, -1);
            }
        }
    }




    private void Move(Vector2Int direction, int startX, int incrementX, int startY, int incrementY)
    {
        bool changed = false;

        for (int x = startX; x >= 0 && x < grid.Width; x += incrementX)
        {
            for (int y = startY; y >= 0 && y < grid.Height; y += incrementY)
            {
                TileCell cell = grid.GetCell(x, y);

                if (cell.Occupied) {
                    changed |= MoveTile(cell.tile, direction);
                }
            }
        }

        if (changed) {
            musicManager.PlayNextNote();
            StartCoroutine(WaitForChanges());
        }
    }

    private bool MoveTile(Tile tile, Vector2Int direction)
    {
        TileCell newCell = null;
        TileCell adjacent = grid.GetAdjacentCell(tile.cell, direction);

        while (adjacent != null)
        {
            if (adjacent.Occupied)
            {
                if (CanMerge(tile, adjacent.tile))
                {
                    MergeTiles(tile, adjacent.tile);
                    return true;
                }

                break;
            }

            newCell = adjacent;
            adjacent = grid.GetAdjacentCell(adjacent, direction);
        }

        if (newCell != null)
        {
            tile.MoveTo(newCell);
            return true;
        }

        return false;
    }

    private bool CanMerge(Tile a, Tile b)
    {
        return a.state == b.state && !b.locked;
    }

    private void MergeTiles(Tile a, Tile b)
    {
        tiles.Remove(a);
        a.Merge(b.cell);

        int index = Mathf.Clamp(IndexOf(b.state) + 1, 0, tileStates.Length - 1);
        TileState newState = tileStates[index];

        b.SetState(newState);
        GameManager.Instance.IncreaseScore(newState.number);
    }

    private int IndexOf(TileState state)
    {
        for (int i = 0; i < tileStates.Length; i++)
        {
            if (state == tileStates[i]) {
                return i;
            }
        }

        return -1;
    }

    private IEnumerator WaitForChanges()
    {
        waiting = true;

        yield return new WaitForSeconds(0.1f);

        waiting = false;

        foreach (var tile in tiles) {
            tile.locked = false;
        }

        if (tiles.Count != grid.Size) {
            CreateTile();
        }

        if (CheckForGameOver()) {
            GameManager.Instance.GameOver();
        }
    }

    public bool CheckForGameOver()
    {
        if (tiles.Count != grid.Size) {
            return false;
        }

        foreach (var tile in tiles)
        {
            TileCell up = grid.GetAdjacentCell(tile.cell, Vector2Int.up);
            TileCell down = grid.GetAdjacentCell(tile.cell, Vector2Int.down);
            TileCell left = grid.GetAdjacentCell(tile.cell, Vector2Int.left);
            TileCell right = grid.GetAdjacentCell(tile.cell, Vector2Int.right);

            if (up != null && CanMerge(tile, up.tile)) {
                return false;
            }

            if (down != null && CanMerge(tile, down.tile)) {
                return false;
            }

            if (left != null && CanMerge(tile, left.tile)) {
                return false;
            }

            if (right != null && CanMerge(tile, right.tile)) {
                return false;
            }
        }

        return true;
    }

}
