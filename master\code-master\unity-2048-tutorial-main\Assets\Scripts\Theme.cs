using UnityEngine;
using System;

[Serializable]
public class TileColorConfig
{
    public int number;
    public Color backgroundColor;
    public Color textColor;
}

[CreateAssetMenu(menuName = "Game Theme")]
public class Theme : ScriptableObject
{
    public string themeName;
    public Color boardBackgroundColor;
    public TileColorConfig[] tileColors;

    public TileColorConfig GetTileConfig(int number)
    {
        foreach (var config in tileColors)
        {
            if (config.number == number)
            {
                return config;
            }
        }
        return null;
    }
}