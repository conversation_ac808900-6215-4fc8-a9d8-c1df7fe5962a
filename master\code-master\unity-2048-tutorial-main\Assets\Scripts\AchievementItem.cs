using UnityEngine;
using TMPro;

public class AchievementItem : MonoBeh<PERSON>our
{
    [SerializeField] private TextMeshProUGUI nameText;
    [SerializeField] private TextMeshProUGUI descriptionText;
    [SerializeField] private TextMeshProUGUI unlockInfoText;

    public void Initialize(string name, string description, string unlockInfo, Color nameColor, Color textColor, bool isUnlocked = true)
    {
        nameText.text = name;
        descriptionText.text = description;
        unlockInfoText.text = unlockInfo;

        if (!isUnlocked)
        {
            // 未解锁使用深灰色
            Color darkGray = new Color(0.3f, 0.3f, 0.3f);
            nameText.color = darkGray;
            descriptionText.color = darkGray;
            unlockInfoText.color = darkGray;
        }
        else
        {
            nameText.color = nameColor;
            descriptionText.color = textColor;
            unlockInfoText.color = textColor;
        }
    }
}