%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5136daff504972940813105a41d458e2, type: 3}
  m_Name: OrangeTheme
  m_EditorClassIdentifier: 
  themeName: Orange
  boardBackgroundColor: {r: 1, g: 0.95, b: 0.9, a: 1}
  tileColors:
  - number: 2
    backgroundColor: {r: 1, g: 0.9, b: 0.8, a: 1}
    textColor: {r: 0.6, g: 0.4, b: 0.2, a: 1}
  - number: 4
    backgroundColor: {r: 1, g: 0.85, b: 0.7, a: 1}
    textColor: {r: 0.6, g: 0.4, b: 0.2, a: 1}
  - number: 8
    backgroundColor: {r: 1, g: 0.7, b: 0.4, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 16
    backgroundColor: {r: 0.95, g: 0.6, b: 0.3, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 32
    backgroundColor: {r: 0.9, g: 0.5, b: 0.2, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 64
    backgroundColor: {r: 0.85, g: 0.4, b: 0.1, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 128
    backgroundColor: {r: 0.8, g: 0.3, b: 0, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 256
    backgroundColor: {r: 0.75, g: 0.25, b: 0, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 512
    backgroundColor: {r: 0.7, g: 0.2, b: 0, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 1024
    backgroundColor: {r: 0.65, g: 0.15, b: 0, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}
  - number: 2048
    backgroundColor: {r: 0.6, g: 0.1, b: 0, a: 1}
    textColor: {r: 1, g: 1, b: 1, a: 1}