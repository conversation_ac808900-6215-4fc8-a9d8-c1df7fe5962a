工作流程=======
粗需求（完整）
ai转细需求
ai拆任务，输出任务文件
ai画原型
ai提供素材包
ai按任务模块实现
ai自己测试
人验证
继续开发

待研究=======
playwright
godot mcp




最新思路：
-类似ptcgp的卡片系统，轻量化的抽卡集卡组合
-自走棋的战斗模式
-简单，爽快，好玩
-玩法有肉鸽地牢，竞技场，擂台赛等等
-经济系统可以自由交易，摆摊等
-还有融合卡机制，产生完全不一样的卡片（上场限定融合卡的数量）
-有没有可能卡牌是完全随机的组合？会不会更爽
-只有卡牌，随机合成，分解，购买随机卡牌等等，稀有度和能力
-卡牌合成，素材合成卡牌等等
-炼金的感觉
-市场买卖卡牌，摆摊

















ai提示词：（react native未使用）
===========================
这是一个空项目，我想使用pixi.js或者其他引擎实现一个可以玩的h5游戏，请你帮我从0到1完整实现，具体逻辑你可以自由发挥，但要确保能玩好玩。
游戏核心内容是如下：
-杀戮尖塔的选择路线肉鸽模式
-自走棋形式的战斗模式
-肉鸽元素
-组合产生随机道具，生物
-生物养成，捕获，搭配
-局外组装培养，反复肉鸽游玩
-多种模式
-多种成就
-多种统计




历史思路======================
-肉鸽闯关生存排兵布阵的游戏

-卡片是有数量的概念的，多套卡组共享，多余的卡片可以分解，也可以研究走线
-卡牌是可以培养的，个性化
-或许卡牌的个性化是随机获得的？无需那么复杂
-每张卡牌可以选择各自的个性化走线
-卡牌专精，消耗该类卡牌（还是卡牌使用次数共享经验？），提升专精等级
-专精等级，解锁卡牌专属的特效和能力等，解锁后可以消耗资源生产和装备
-专精等级，还能解锁卡牌可使用的天赋点数，用来定制卡牌个性化走线
-每张卡牌可以独立设置别名，收藏和分类
--卡牌特性，在卡牌培养有研究线路中解锁，解锁后被动作用
-卡牌有1-3，三个等级，升级模式类似自走棋，走线选择
-肉鸽走线模式，生存模式（有消耗），卡组可以初始搭配15张卡，游戏过程中可以扩充至最多30张卡
-还有竞技场，擂台赛（自动），押注模式
-成就和统计





-轻量化的抽卡集卡组合自走对战的游戏
-ptcg样式自动一段时间自动抽牌上场（有抽牌速度的概念），自走棋模式战斗
-宝可梦类似的生物卡，装备卡，道具卡，效果卡（自动）
-卡片搜集，卡组搭配，场外培养，自定义培养
-卡组可以初始搭配15张卡，游戏过程中可以扩充至最多30张卡
-卡牌有1-3，三个等级，升级模式类似自走棋
-唤灵师培养（天赋，技能搭配，命石等，可出战，被消灭就失败了），也就是职业，可以主动操作，技能，道具，装备，可以自动
-肉鸽走线模式，生存模式（有消耗）
-还有竞技场，擂台赛（自动），押注模式












-背包战争类型，排兵布阵，自动战斗
-兵种范围配合，兵种组合合成，场外兵种升级
-合成时有些是随机合成
-双英雄，主附将，作为主将时，领袖技能生效，历史名将
-属于某种组合的英雄或者作战单位相邻时，会形成作战组合，组合拥有特殊效果
-将领装备是可以养成的，还可以刷天赋，搭配技能等
-竞技场模式，固定点数自由组装无尽竞技场，竞技场或许可以用玩家配置
-多种模式
-多种成就
-多种统计



粗需求：
1.主要模块
-英雄
-属性有，HP，耐力（物理攻击），法力（使用技能）
-HP，小于等于0时，单位将无法进行任何行动
-耐力，所有英雄的基础耐力都是100，当耐力大于等于50时，属于亢奋状态，攻击速度提升30%，攻击时消耗耐力提升50%
-耐力，小于50时，失去亢奋状态
-耐力，非攻击时，耐力默认每秒恢复5，攻击时为1（1也是下限）
-耐力，当前耐力不足以维持一次进攻时，就只能等待耐力恢复（所以要考虑装备搭配，比如双持，双手，单手武器，还有技能是当攻击时没有耐力，将会进行格挡，将攻击力的25%转化为格挡值，持续1秒，不叠加）



-1个领袖技（作为主将时生效），3个其他技能（可能是主动也可能是被动）



-军营

-战斗

-道具

-商店




